import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Navigation } from './components/navigation';
import { Hero } from './components/sections/hero';
import YachtShowcase from './components/sections/yacht-showcase';
import Statistics from './components/sections/statistics';
import Team from './components/sections/team';
import Footer from './components/sections/footer';
import { PhoneIcon } from './components/icons';
import './App.css';

function App() {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('home');

  const renderContent = () => {
    switch (activeTab) {
      case 'home':
        return (
          <>
            {/* Hero Section */}
            <Hero />

            {/* Yacht Showcase Section */}
            <YachtShowcase />

            {/* Statistics Section */}
            <Statistics />

            {/* Team Section */}
            <Team />

            {/* Featured Destinations */}
            <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
              <div className="container mx-auto px-4">
                <div className="text-center mb-16">
                  <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium mb-4">
                    🏝️ Destinations
                  </div>
                  <h2 className="text-4xl md:text-5xl font-serif font-bold text-gray-800 mb-6">
                    {t('home.featured.title', { defaultValue: 'Discover Auckland Waters' })}
                  </h2>
                  <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    {t('home.featured.subtitle', {
                      defaultValue: "Explore pristine islands, hidden coves, and stunning coastlines across Auckland's magnificent Hauraki Gulf."
                    })}
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {[
                    {
                      key: 'waiheke',
                      name: 'Waiheke Island',
                      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                      description: 'Famous for its vineyards, beaches, and artistic community'
                    },
                    {
                      key: 'rangitoto',
                      name: 'Rangitoto Island',
                      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                      description: 'Volcanic island with unique landscapes and hiking trails'
                    },
                    {
                      key: 'kawau',
                      name: 'Kawau Island',
                      image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                      description: 'Historic island with beautiful bays and wildlife'
                    }
                  ].map((destination) => (
                    <div key={destination.key} className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden border border-gray-100">
                      <div className="h-64 overflow-hidden">
                        <img
                          src={destination.image}
                          alt={destination.name}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                        />
                      </div>
                      <div className="p-6">
                        <h3 className="text-2xl font-serif font-bold mb-3 text-gray-800">
                          {destination.name}
                        </h3>
                        <p className="text-gray-600 mb-6 leading-relaxed">
                          {destination.description}
                        </p>
                        <Button
                          variant="outline"
                          className="w-full border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400 font-medium py-3 rounded-xl transition-all duration-200"
                        >
                          {t('home.featured.destinations.viewDetails', { defaultValue: 'Explore Destination' })}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </section>
          </>
        );

      case 'destinations':
        return (
          <section className="pt-20 pb-16 bg-gradient-to-b from-blue-50 to-white min-h-screen">
            <div className="container mx-auto px-4">
              <div className="text-center mb-16">
                <h1 className="text-4xl md:text-5xl font-serif font-bold text-gray-800 mb-6">
                  Sailing Destinations
                </h1>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  Discover the most beautiful sailing destinations around Auckland's stunning coastline.
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[
                  {
                    name: 'Waiheke Island',
                    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                    description: 'Famous for its vineyards, beaches, and artistic community. Perfect for day trips with wine tasting and beautiful beaches.',
                    duration: '4-8 hours',
                    highlights: ['Wine Tasting', 'Beautiful Beaches', 'Art Galleries', 'Restaurants']
                  },
                  {
                    name: 'Rangitoto Island',
                    image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                    description: 'Volcanic island with unique landscapes and hiking trails. Explore lava caves and enjoy panoramic views.',
                    duration: '6 hours',
                    highlights: ['Volcanic Landscape', 'Hiking Trails', 'Lava Caves', 'City Views']
                  },
                  {
                    name: 'Kawau Island',
                    image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                    description: 'Historic island with beautiful bays and wildlife. Rich history and pristine natural environment.',
                    duration: 'Full Day',
                    highlights: ['Historic Sites', 'Wildlife', 'Secluded Bays', 'Walking Tracks']
                  }
                ].map((destination, index) => (
                  <div key={index} className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
                    <div className="h-64 overflow-hidden">
                      <img
                        src={destination.image}
                        alt={destination.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="p-6">
                      <h3 className="text-2xl font-serif font-bold mb-3 text-gray-800">
                        {destination.name}
                      </h3>
                      <p className="text-gray-600 mb-4">
                        {destination.description}
                      </p>
                      <div className="mb-4">
                        <span className="text-sm font-semibold text-blue-600">Duration: {destination.duration}</span>
                      </div>
                      <div className="mb-6">
                        <h4 className="text-sm font-semibold text-gray-700 mb-2">Highlights:</h4>
                        <div className="flex flex-wrap gap-2">
                          {destination.highlights.map((highlight, idx) => (
                            <span key={idx} className="bg-blue-100 text-blue-700 px-2 py-1 rounded-md text-xs">
                              {highlight}
                            </span>
                          ))}
                        </div>
                      </div>
                      <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                        Book This Destination
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        );

      case 'yachts':
        return (
          <section className="pt-20 pb-16 min-h-screen">
            <YachtShowcase />
          </section>
        );

      case 'about':
        return (
          <section className="pt-20 pb-16 min-h-screen">
            <Team />
            <Statistics />
          </section>
        );

      case 'contact':
        return (
          <section className="pt-20 pb-16 bg-gradient-to-b from-blue-50 to-white min-h-screen">
            <div className="container mx-auto px-4">
              <div className="text-center mb-16">
                <h1 className="text-4xl md:text-5xl font-serif font-bold text-gray-800 mb-6">
                  Contact Us
                </h1>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  Ready to embark on your sailing adventure? Get in touch with us today.
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                {/* Contact Information */}
                <div className="space-y-8">
                  <div className="bg-white rounded-2xl shadow-lg p-8">
                    <h3 className="text-2xl font-serif font-bold text-gray-800 mb-6">Get In Touch</h3>
                    <div className="space-y-4">
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                          <PhoneIcon className="w-6 h-6 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-semibold text-gray-800">Phone</p>
                          <p className="text-gray-600">+64 9 123 4567</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                          <span className="text-blue-600 text-xl">✉️</span>
                        </div>
                        <div>
                          <p className="font-semibold text-gray-800">Email</p>
                          <p className="text-gray-600"><EMAIL></p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                          <span className="text-blue-600 text-xl">📍</span>
                        </div>
                        <div>
                          <p className="font-semibold text-gray-800">Location</p>
                          <p className="text-gray-600">Viaduct Harbour, Auckland, New Zealand</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Contact Form */}
                <div className="bg-white rounded-2xl shadow-lg p-8">
                  <h3 className="text-2xl font-serif font-bold text-gray-800 mb-6">Send us a Message</h3>
                  <form className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                      <input
                        type="text"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                      <input
                        type="email"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
                      <textarea
                        rows={4}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Tell us about your sailing adventure plans..."
                      ></textarea>
                    </div>
                    <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3">
                      Send Message
                    </Button>
                  </form>
                </div>
              </div>
            </div>
          </section>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Navigation activeTab={activeTab} onTabChange={setActiveTab} />
      <main>
        {renderContent()}
      </main>
      <Footer />
    </div>
  );
}

export default App;
