import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Navigation } from './components/navigation';
import { Hero } from './components/sections/hero';
import { Experiences } from './components/sections/experiences';
import YachtShowcase from './components/sections/yacht-showcase';
import Statistics from './components/sections/statistics';
import Team from './components/sections/team';
import Footer from './components/sections/footer';
import './App.css';

function App() {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('home');

  return (
    <div className="min-h-screen bg-white">
      <Navigation activeTab={activeTab} onTabChange={setActiveTab} />

      <main>
        {/* Hero Section */}
        <Hero />

        {/* Yacht Showcase Section */}
        <YachtShowcase />

        {/* Statistics Section */}
        <Statistics />

        {/* Experiences Section */}
        <Experiences />

        {/* Team Section */}
        <Team />

        {/* Featured Destinations - Updated with sailing theme */}
        <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium mb-4">
                🏝️ Destinations
              </div>
              <h2 className="text-4xl md:text-5xl font-serif font-bold text-gray-800 mb-6">
                {t('home.featured.title', { defaultValue: 'Discover Auckland Waters' })}
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {t('home.featured.subtitle', {
                  defaultValue: "Explore pristine islands, hidden coves, and stunning coastlines across Auckland's magnificent Hauraki Gulf."
                })}
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  key: 'waiheke',
                  name: 'Waiheke Island',
                  image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                  description: 'Famous for its vineyards, beaches, and artistic community'
                },
                {
                  key: 'rangitoto',
                  name: 'Rangitoto Island',
                  image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                  description: 'Volcanic island with unique landscapes and hiking trails'
                },
                {
                  key: 'kawau',
                  name: 'Kawau Island',
                  image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                  description: 'Historic island with beautiful bays and wildlife'
                }
              ].map((destination) => (
                <div key={destination.key} className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden border border-gray-100">
                  <div className="h-64 overflow-hidden">
                    <img
                      src={destination.image}
                      alt={destination.name}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                  </div>
                  <div className="p-6">
                    <h3 className="text-2xl font-serif font-bold mb-3 text-gray-800">
                      {destination.name}
                    </h3>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      {destination.description}
                    </p>
                    <Button
                      variant="outline"
                      className="w-full border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400 font-medium py-3 rounded-xl transition-all duration-200"
                    >
                      {t('home.featured.destinations.viewDetails', { defaultValue: 'Explore Destination' })}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}

export default App;
