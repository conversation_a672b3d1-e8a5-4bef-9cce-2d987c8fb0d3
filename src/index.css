@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,400&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply font-sans;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-serif font-bold;
  }

  body {
    @apply antialiased text-gray-800 bg-white;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }
}

@layer base {
  :root {
    /* Sailing-inspired color palette */
    --background: 255 255% 100%;
    --foreground: 203 23% 30%;
    --card: 0 0% 100%;
    --card-foreground: 203 23% 30%;
    --popover: 0 0% 100%;
    --popover-foreground: 203 23% 30%;

    /* Ocean Blue Primary */
    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 100%;

    /* Light Ocean Secondary */
    --secondary: 200 100% 97%;
    --secondary-foreground: 203 23% 30%;

    /* Muted Ocean Tones */
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    /* Accent - Deep Ocean */
    --accent: 215 84% 45%;
    --accent-foreground: 0 0% 100%;

    /* Nautical Red for alerts */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Borders and inputs */
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 210 100% 50%;
    --radius: 0.75rem;

    /* Custom sailing colors */
    --ocean-blue: 210 100% 50%;
    --deep-ocean: 215 84% 45%;
    --light-ocean: 200 100% 97%;
    --nautical-navy: 220 39% 11%;
    --sea-foam: 174 72% 56%;
    --sunset-orange: 25 95% 53%;
    --sail-white: 0 0% 100%;
  }

  .dark {
    --background: 220 39% 11%;
    --foreground: 0 0% 95%;
    --card: 220 39% 11%;
    --card-foreground: 0 0% 95%;
    --popover: 220 39% 11%;
    --popover-foreground: 0 0% 95%;
    --primary: 210 100% 60%;
    --primary-foreground: 220 39% 11%;
    --secondary: 215 28% 17%;
    --secondary-foreground: 0 0% 95%;
    --muted: 215 28% 17%;
    --muted-foreground: 215 20% 65%;
    --accent: 215 28% 17%;
    --accent-foreground: 0 0% 95%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 0 0% 95%;
    --border: 215 28% 17%;
    --input: 215 28% 17%;
    --ring: 210 100% 60%;
  }
}

@layer base {
  * {
    @apply border-gray-200;
  }
  body {
    @apply bg-white text-gray-800;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar with ocean theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: #f3f4f6; /* bg-gray-100 */
}

::-webkit-scrollbar-thumb {
  background-color: #0ea5e9; /* ocean blue */
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #0284c7; /* deeper ocean blue */
}

/* Selection styling with ocean theme */
::selection {
  background-color: rgba(14, 165, 233, 0.2); /* ocean blue/20 */
  color: #1f2937; /* gray-800 */
}

/* Focus styles with ocean theme */
*:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.5), 0 0 0 4px rgba(255, 255, 255, 0.8);
}

/* Sailing-themed animations */
@keyframes wave {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes sail {
  0% { transform: translateX(-100px) rotate(-5deg); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100vw) rotate(5deg); opacity: 0; }
}

@keyframes ripple {
  0% { transform: scale(0); opacity: 1; }
  100% { transform: scale(4); opacity: 0; }
}

/* Utility classes for sailing theme */
.wave-animation {
  animation: wave 3s ease-in-out infinite;
}

.sail-animation {
  animation: sail 20s linear infinite;
}

.ocean-gradient {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
}

.sunset-gradient {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 50%, #dc2626 100%);
}

.sea-foam-gradient {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 50%, #0e7490 100%);
}

/* Typography enhancements */
.font-sailing {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.text-ocean {
  color: #0ea5e9;
}

.text-deep-ocean {
  color: #0284c7;
}

.text-nautical-navy {
  color: #1e293b;
}

/* Button enhancements */
.btn-ocean {
  @apply bg-blue-500 hover:bg-blue-600 text-white font-medium px-6 py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1;
}

.btn-outline-ocean {
  @apply border-2 border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white font-medium px-6 py-3 rounded-xl transition-all duration-300;
}

/* Card enhancements */
.card-sailing {
  @apply bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100;
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }
}

@media (min-width: 769px) {
  .hero-title {
    font-size: 4rem;
    line-height: 1.1;
  }
}
