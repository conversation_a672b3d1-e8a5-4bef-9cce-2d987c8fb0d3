/* Reset default styles that might interfere with layout */
#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Ensure full width for main content */
main {
  flex: 1;
}

/* Reset default button styles */
button, input[type="button"], input[type="submit"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

/* Ensure images are responsive */
img {
  max-width: 100%;
  height: auto;
}

/* Base typography */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  color: #1a1a1a;
  background-color: #ffffff;
}

/* Links */
a {
  color: #2563eb;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Hero section styles */
.hero-title {
  font-size: 3rem;
  color: white !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  z-index: 50;
  position: relative;
}

@media (min-width: 768px) {
  .hero-title {
    font-size: 4rem;
  }
}

@media (min-width: 1024px) {
  .hero-title {
    font-size: 5rem;
  }
}

/* Wave animation */
@keyframes wave {
  0%, 100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-25px);
  }
}

.animate-wave {
  animation: wave 3s ease-in-out infinite;
}
