import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import { getGalleryImages, getCategories, getTripTypes } from '@/api/gallery';
import { ImageGrid } from './ImageGrid';
import { Lightbox } from './Lightbox';
import { GalleryFilters } from './GalleryFilters';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
export const Gallery = () => {
    const [searchParams, setSearchParams] = useSearchParams();
    const [images, setImages] = useState([]);
    const [categories, setCategories] = useState([]);
    const [tripTypes, setTripTypes] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [lightboxOpen, setLightboxOpen] = useState(false);
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [totalImages, setTotalImages] = useState(0);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    // Initialize filters from URL params or use defaults
    const [filters, setFilters] = useState({
        category: searchParams.get('category') || undefined,
        tripType: searchParams.get('tripType') || undefined,
        searchQuery: searchParams.get('q') || undefined,
        sortBy: searchParams.get('sort') || 'newest',
    });
    const itemsPerPage = 24; // Number of images to load per page
    // Fetch gallery data
    const fetchGalleryData = useCallback(async (resetPagination = false) => {
        try {
            setIsLoading(true);
            setError(null);
            const currentPage = resetPagination ? 1 : page;
            // Update URL params
            const params = new URLSearchParams();
            if (filters.category)
                params.set('category', filters.category);
            if (filters.tripType)
                params.set('tripType', filters.tripType);
            if (filters.searchQuery)
                params.set('q', filters.searchQuery);
            if (filters.sortBy && filters.sortBy !== 'newest')
                params.set('sort', filters.sortBy);
            // Only push to history if params have changed
            if (params.toString() !== searchParams.toString()) {
                setSearchParams(params, { replace: true });
            }
            // Fetch images with current filters and pagination
            const response = await getGalleryImages({
                ...filters,
                // Only include search query if it's not empty
                searchQuery: filters.searchQuery?.trim() || undefined,
            }, currentPage, itemsPerPage);
            // If this is the first page or we're resetting, replace the images
            // Otherwise, append to the existing images for infinite scroll
            setImages(prevImages => resetPagination || currentPage === 1
                ? response.images
                : [...prevImages, ...response.images]);
            setTotalImages(response.total);
            setHasMore(currentPage < response.totalPages);
            // Only fetch categories and trip types if we don't have them yet
            if (categories.length === 0) {
                const [categoriesData, tripTypesData] = await Promise.all([
                    getCategories(),
                    getTripTypes(),
                ]);
                setCategories(categoriesData);
                setTripTypes(tripTypesData);
            }
        }
        catch (err) {
            console.error('Error fetching gallery data:', err);
            setError('Failed to load gallery. Please try again later.');
        }
        finally {
            setIsLoading(false);
        }
    }, [filters, page, itemsPerPage, categories.length, searchParams, setSearchParams]);
    // Initial data fetch
    useEffect(() => {
        fetchGalleryData(true);
        // Cleanup function to cancel any pending requests
        return () => {
            // Add any cleanup code if needed (e.g., cancel fetch requests)
        };
    }, [fetchGalleryData]);
    // Handle filter changes
    const handleFilterChange = (newFilters) => {
        setFilters(prev => ({
            ...prev,
            ...newFilters,
        }));
        // Reset to first page when filters change
        if (page !== 1) {
            setPage(1);
        }
        // If we're already on page 1, trigger a refetch
        if (page === 1) {
            fetchGalleryData(true);
        }
    };
    // Reset all filters
    const resetFilters = () => {
        setFilters({
            category: undefined,
            tripType: undefined,
            searchQuery: undefined,
            sortBy: 'newest',
        });
        if (page !== 1) {
            setPage(1);
        }
        else {
            fetchGalleryData(true);
        }
    };
    // Handle image click to open lightbox
    const handleImageClick = (image, index) => {
        setCurrentImageIndex(images.findIndex(img => img.id === image.id));
        setLightboxOpen(true);
    };
    // Handle lightbox navigation
    const handleLightboxIndexChange = (index) => {
        setCurrentImageIndex(index);
    };
    // Handle infinite scroll
    const handleLoadMore = () => {
        if (!isLoading && hasMore) {
            setPage(prev => prev + 1);
        }
    };
    // Set up infinite scroll effect
    useEffect(() => {
        if (page > 1) {
            fetchGalleryData(false);
        }
    }, [page, fetchGalleryData]);
    // Prepare lightbox images
    const lightboxImages = images.map((img, index) => ({
        ...img,
        index,
        nextId: index < images.length - 1 ? images[index + 1]?.id : null,
        prevId: index > 0 ? images[index - 1]?.id : null,
    }));
    return (_jsx("div", { className: "container mx-auto px-4 py-8", children: _jsxs("div", { className: "max-w-7xl mx-auto", children: [_jsxs("header", { className: "mb-8 text-center", children: [_jsx("h1", { className: "text-4xl font-bold text-gray-900 dark:text-white mb-2", children: "Sailing Gallery" }), _jsx("p", { className: "text-xl text-muted-foreground", children: "Explore our collection of stunning sailing moments" })] }), _jsx("div", { className: "mb-8", children: _jsx(GalleryFilters, { filters: filters, categories: categories, tripTypes: tripTypes, onFilterChange: handleFilterChange, onReset: resetFilters, isLoading: isLoading }) }), error && (_jsxs("div", { className: "bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg mb-6", children: [error, _jsx(Button, { variant: "ghost", size: "sm", className: "ml-4", onClick: () => fetchGalleryData(true), children: "Try Again" })] })), _jsx("div", { className: "mb-8", children: images.length > 0 ? (_jsxs(_Fragment, { children: [_jsx(ImageGrid, { images: images, onImageClick: handleImageClick, loading: isLoading && page === 1, className: "mb-8", columns: {
                                    sm: 2,
                                    md: 3,
                                    lg: 4,
                                }, gap: {
                                    sm: 'gap-3',
                                    md: 'gap-4',
                                    lg: 'gap-5',
                                } }), hasMore && (_jsx("div", { className: "text-center mt-8", children: _jsx(Button, { onClick: handleLoadMore, disabled: isLoading, className: "min-w-[150px]", children: isLoading ? (_jsxs(_Fragment, { children: [_jsx(Loader2, { className: "h-4 w-4 mr-2 animate-spin" }), "Loading..."] })) : ('Load More') }) })), _jsxs("div", { className: "text-center text-sm text-muted-foreground mt-4", children: ["Showing ", images.length, " of ", totalImages, " images"] })] })) : !isLoading ? (_jsxs("div", { className: "text-center py-12", children: [_jsx("div", { className: "text-5xl mb-4", children: "\uD83D\uDDBC\uFE0F" }), _jsx("h3", { className: "text-xl font-medium text-gray-900 dark:text-white mb-2", children: "No images found" }), _jsx("p", { className: "text-muted-foreground mb-4", children: "Try adjusting your search or filter criteria" }), _jsx(Button, { variant: "outline", onClick: resetFilters, children: "Clear all filters" })] })) : null }), lightboxImages.length > 0 && (_jsx(Lightbox, { isOpen: lightboxOpen, onClose: () => setLightboxOpen(false), images: lightboxImages, initialIndex: currentImageIndex, onIndexChange: handleLightboxIndexChange, showThumbnails: true, showCaption: true, showDownload: true, showFullscreen: true }))] }) }));
};
export default Gallery;
