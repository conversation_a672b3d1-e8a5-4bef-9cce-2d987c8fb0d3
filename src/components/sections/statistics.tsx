import React from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { YachtIcon, UserIcon, AnchorIcon, StarIcon } from '../icons';

interface StatisticsProps {
  className?: string;
}

interface Statistic {
  id: string;
  value: string;
  label: string;
  icon: React.ReactNode;
  description: string;
}

const statistics: Statistic[] = [
  {
    id: 'happy-sailors',
    value: '2,500+',
    label: 'Happy Sailors',
    icon: <UserIcon className="w-8 h-8" />,
    description: 'Satisfied customers who experienced unforgettable sailing adventures'
  },
  {
    id: 'luxury-yachts',
    value: '25+',
    label: 'Luxury Yachts',
    icon: <YachtIcon className="w-8 h-8" />,
    description: 'Premium sailing vessels in our carefully maintained fleet'
  },
  {
    id: 'destinations',
    value: '150+',
    label: 'Destinations',
    icon: <AnchorIcon className="w-8 h-8" />,
    description: 'Stunning locations across Auckland waters and beyond'
  },
  {
    id: 'rating',
    value: '4.9★',
    label: 'Average Rating',
    icon: <StarIcon className="w-8 h-8" />,
    description: 'Consistently excellent reviews from our valued guests'
  }
];

const features = [
  {
    title: 'Professional Crew',
    description: 'Experienced captains and crew members ensure your safety and comfort',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
  },
  {
    title: 'Premium Amenities',
    description: 'Luxury facilities including gourmet catering and water sports equipment',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
  },
  {
    title: 'Scenic Routes',
    description: 'Carefully planned routes showcasing Auckland\'s most beautiful coastal areas',
    image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
  }
];

export function Statistics({ className }: StatisticsProps) {
  const { t } = useTranslation();

  return (
    <section className={cn("py-20 bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 relative overflow-hidden", className)}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          <defs>
            <pattern id="waves" x="0" y="0" width="100" height="20" patternUnits="userSpaceOnUse">
              <path d="M0 10 Q25 0 50 10 T100 10 V20 H0 Z" fill="white" />
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#waves)" />
        </svg>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Statistics Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {statistics.map((stat, index) => (
            <div 
              key={stat.id}
              className="text-center group"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="bg-white/20 backdrop-blur-md rounded-2xl p-6 border border-white/30 hover:bg-white/30 transition-all duration-300 transform hover:-translate-y-2">
                <div className="text-white/80 mb-4 flex justify-center group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                <div className="text-4xl md:text-5xl font-bold text-white mb-2 font-serif">
                  {stat.value}
                </div>
                <div className="text-lg font-semibold text-white/90 mb-2">
                  {t(`statistics.${stat.id}.label`, { defaultValue: stat.label })}
                </div>
                <div className="text-sm text-white/70 leading-relaxed">
                  {t(`statistics.${stat.id}.description`, { defaultValue: stat.description })}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Features Section */}
        <div className="text-center mb-12">
          <h3 className="text-3xl md:text-4xl font-serif font-bold text-white mb-4">
            {t('statistics.features.title', { defaultValue: 'Why Choose Sailing Serai?' })}
          </h3>
          <p className="text-xl text-white/90 max-w-3xl mx-auto">
            {t('statistics.features.subtitle', { 
              defaultValue: 'Experience the difference with our commitment to excellence, safety, and unforgettable memories.' 
            })}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="group"
              style={{ animationDelay: `${(index + 4) * 0.1}s` }}
            >
              <div className="bg-white/10 backdrop-blur-md rounded-2xl overflow-hidden border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2">
                <div className="h-48 overflow-hidden">
                  <img 
                    src={feature.image} 
                    alt={feature.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                </div>
                <div className="p-6">
                  <h4 className="text-xl font-serif font-bold text-white mb-3">
                    {feature.title}
                  </h4>
                  <p className="text-white/80 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-10 right-10 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white/10 rounded-full blur-lg"></div>
    </section>
  );
}

export default Statistics;
