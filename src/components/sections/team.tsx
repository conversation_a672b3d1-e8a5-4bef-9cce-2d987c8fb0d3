import React from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { AnchorIcon, StarIcon } from '../icons';

interface TeamProps {
  className?: string;
}

interface TeamMember {
  id: string;
  name: string;
  role: string;
  experience: string;
  specialties: string[];
  image: string;
  bio: string;
  certifications: string[];
}

const teamMembers: TeamMember[] = [
  {
    id: 'captain-james',
    name: 'Captain <PERSON>',
    role: 'Master Mariner',
    experience: '25+ Years',
    specialties: ['Ocean Racing', 'Luxury Charters', 'Navigation'],
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    bio: 'With over two decades of sailing experience, Captain <PERSON> has navigated waters across the Pacific and leads our team with unmatched expertise.',
    certifications: ['RYA Yachtmaster Ocean', 'MCA Master 200GT', 'STCW Advanced']
  },
  {
    id: 'captain-sarah',
    name: 'Captain <PERSON>',
    role: 'Charter Specialist',
    experience: '15+ Years',
    specialties: ['Sunset Cruises', 'Corporate Events', 'Safety Training'],
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    bio: 'Captain Sarah specializes in creating memorable experiences for our guests while maintaining the highest safety standards.',
    certifications: ['RYA Yachtmaster Offshore', 'First Aid Instructor', 'VHF Radio Operator']
  },
  {
    id: 'first-mate-alex',
    name: 'Alex Thompson',
    role: 'First Mate',
    experience: '12+ Years',
    specialties: ['Yacht Maintenance', 'Guest Services', 'Water Sports'],
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    bio: 'Alex ensures our fleet is always in perfect condition and helps guests make the most of their sailing adventure.',
    certifications: ['RYA Day Skipper', 'Powerboat Level 2', 'Marine Engineering']
  },
  {
    id: 'chef-maria',
    name: 'Chef Maria Rodriguez',
    role: 'Executive Chef',
    experience: '18+ Years',
    specialties: ['Gourmet Cuisine', 'Dietary Requirements', 'Local Seafood'],
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    bio: 'Chef Maria creates exceptional dining experiences using the finest local ingredients and international culinary techniques.',
    certifications: ['Culinary Arts Degree', 'Food Safety Certified', 'Wine Sommelier']
  }
];

export function Team({ className }: TeamProps) {
  const { t } = useTranslation();

  return (
    <section className={cn("py-20 bg-gradient-to-b from-white to-slate-50", className)}>
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-ocean-100 text-ocean-700 rounded-full text-sm font-medium mb-4">
            <AnchorIcon className="w-4 h-4 mr-2" />
            Our Crew
          </div>
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-nautical-800 mb-6">
            {t('team.title', { defaultValue: 'Meet Our Expert Team' })}
          </h2>
          <p className="text-xl text-nautical-600 max-w-3xl mx-auto leading-relaxed">
            {t('team.subtitle', { 
              defaultValue: 'Our experienced crew members are passionate about sailing and dedicated to providing you with an exceptional maritime experience.' 
            })}
          </p>
        </div>

        {/* Team Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {teamMembers.map((member, index) => (
            <div 
              key={member.id}
              className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden border border-slate-100"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* Member Photo */}
              <div className="relative h-80 overflow-hidden">
                <img 
                  src={member.image} 
                  alt={member.name}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-ocean-600 font-medium text-sm">{member.role}</span>
                      <div className="flex items-center text-yellow-500">
                        <StarIcon className="w-4 h-4 fill-current" />
                        <span className="text-xs ml-1 text-nautical-600">{member.experience}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Member Details */}
              <div className="p-6">
                <h3 className="text-xl font-serif font-bold text-nautical-800 mb-2">
                  {member.name}
                </h3>
                <p className="text-nautical-600 mb-4 leading-relaxed text-sm">
                  {member.bio}
                </p>

                {/* Specialties */}
                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-nautical-700 mb-2">Specialties:</h4>
                  <div className="flex flex-wrap gap-1">
                    {member.specialties.map((specialty, idx) => (
                      <span 
                        key={idx}
                        className="bg-ocean-50 text-ocean-700 px-2 py-1 rounded-md text-xs font-medium"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Certifications */}
                <div>
                  <h4 className="text-sm font-semibold text-nautical-700 mb-2">Certifications:</h4>
                  <div className="space-y-1">
                    {member.certifications.slice(0, 2).map((cert, idx) => (
                      <div key={idx} className="text-xs text-nautical-600 flex items-center">
                        <div className="w-1.5 h-1.5 bg-ocean-500 rounded-full mr-2"></div>
                        {cert}
                      </div>
                    ))}
                    {member.certifications.length > 2 && (
                      <div className="text-xs text-nautical-500">
                        +{member.certifications.length - 2} more certifications
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Team Values */}
        <div className="bg-ocean-50 rounded-2xl p-8 md:p-12">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-serif font-bold text-nautical-800 mb-4">
              {t('team.values.title', { defaultValue: 'Our Commitment to Excellence' })}
            </h3>
            <p className="text-lg text-nautical-600 max-w-2xl mx-auto">
              {t('team.values.subtitle', { 
                defaultValue: 'Every member of our team shares a passion for the sea and a commitment to creating unforgettable experiences.' 
              })}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-ocean-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <AnchorIcon className="w-8 h-8 text-white" />
              </div>
              <h4 className="text-lg font-semibold text-nautical-800 mb-2">Safety First</h4>
              <p className="text-nautical-600 text-sm">
                All crew members are certified professionals with extensive safety training and emergency response experience.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-ocean-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <StarIcon className="w-8 h-8 text-white" />
              </div>
              <h4 className="text-lg font-semibold text-nautical-800 mb-2">Service Excellence</h4>
              <p className="text-nautical-600 text-sm">
                We go above and beyond to ensure every guest feels welcomed and enjoys a truly memorable sailing experience.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-ocean-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <AnchorIcon className="w-8 h-8 text-white" />
              </div>
              <h4 className="text-lg font-semibold text-nautical-800 mb-2">Local Expertise</h4>
              <p className="text-nautical-600 text-sm">
                Our team's deep knowledge of Auckland waters ensures you discover the most beautiful and secluded spots.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default Team;
