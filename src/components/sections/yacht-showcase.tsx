import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ArrowRightIcon, UserIcon, AnchorIcon } from '../icons';

interface YachtShowcaseProps {
  className?: string;
}

interface Yacht {
  id: string;
  name: string;
  type: string;
  capacity: number;
  length: string;
  price: string;
  image: string;
  features: string[];
  description: string;
}

const yachts: Yacht[] = [
  {
    id: 'ocean-breeze',
    name: 'Ocean Breeze',
    type: 'Luxury Catamaran',
    capacity: 12,
    length: '45ft',
    price: 'From $2,500/day',
    image: 'https://images.unsplash.com/photo-1567899378494-47b22a2ae96a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    features: ['Full Kitchen', 'Sun Deck', 'Snorkeling Gear', 'Sound System'],
    description: 'Perfect for day trips and sunset cruises with spacious deck areas and modern amenities.'
  },
  {
    id: 'sea-spirit',
    name: 'Sea Spirit',
    type: 'Racing Yacht',
    capacity: 8,
    length: '38ft',
    price: 'From $1,800/day',
    image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    features: ['High Performance', 'Racing Sails', 'GPS Navigation', 'Safety Equipment'],
    description: 'Experience the thrill of sailing with our high-performance racing yacht designed for speed and agility.'
  },
  {
    id: 'azure-dream',
    name: 'Azure Dream',
    type: 'Motor Yacht',
    capacity: 16,
    length: '55ft',
    price: 'From $3,200/day',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    features: ['Luxury Cabins', 'Jacuzzi', 'Chef Service', 'Water Sports'],
    description: 'Ultimate luxury experience with premium amenities and professional crew service for unforgettable journeys.'
  },
  {
    id: 'wind-dancer',
    name: 'Wind Dancer',
    type: 'Classic Sailboat',
    capacity: 6,
    length: '32ft',
    price: 'From $1,200/day',
    image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    features: ['Traditional Sailing', 'Cozy Interior', 'Fishing Equipment', 'Sunset Views'],
    description: 'Authentic sailing experience with traditional rigging and intimate setting for small groups.'
  },
  {
    id: 'island-hopper',
    name: 'Island Hopper',
    type: 'Adventure Catamaran',
    capacity: 20,
    length: '50ft',
    price: 'From $2,800/day',
    image: 'https://images.unsplash.com/photo-1566024287286-457247b70310?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    features: ['Large Capacity', 'Diving Platform', 'BBQ Facilities', 'Shade Areas'],
    description: 'Perfect for group adventures and island hopping with excellent stability and comfort.'
  },
  {
    id: 'midnight-express',
    name: 'Midnight Express',
    type: 'Sport Cruiser',
    capacity: 10,
    length: '42ft',
    price: 'From $2,200/day',
    image: 'https://images.unsplash.com/photo-1540946485063-a40da27545f8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    features: ['Speed Cruising', 'Modern Design', 'Entertainment System', 'Comfortable Seating'],
    description: 'Sleek and fast cruiser perfect for those who want to cover more distance in style and comfort.'
  }
];

export function YachtShowcase({ className }: YachtShowcaseProps) {
  const { t } = useTranslation();

  return (
    <section className={cn("py-20 bg-gradient-to-b from-gray-50 to-white", className)}>
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium mb-4">
            <AnchorIcon className="w-4 h-4 mr-2" />
            Our Fleet
          </div>
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-gray-800 mb-6">
            {t('yachts.title', { defaultValue: 'Luxury Sailing Fleet' })}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t('yachts.subtitle', {
              defaultValue: 'Choose from our carefully curated collection of premium sailing vessels, each offering unique experiences and unmatched comfort on the water.'
            })}
          </p>
        </div>

        {/* Yacht Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {yachts.map((yacht) => (
            <div
              key={yacht.id}
              className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden border border-gray-100"
            >
              {/* Yacht Image */}
              <div className="relative h-64 overflow-hidden">
                <img 
                  src={yacht.image} 
                  alt={yacht.name}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-ocean-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {yacht.type}
                  </span>
                </div>
                <div className="absolute top-4 right-4">
                  <span className="bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-sm font-bold">
                    {yacht.price}
                  </span>
                </div>
              </div>

              {/* Yacht Details */}
              <div className="p-6">
                <h3 className="text-2xl font-serif font-bold text-gray-800 mb-2">
                  {yacht.name}
                </h3>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {yacht.description}
                </p>

                {/* Yacht Specs */}
                <div className="flex items-center justify-between mb-4 text-sm text-gray-600">
                  <div className="flex items-center">
                    <UserIcon className="w-4 h-4 mr-1" />
                    <span>{yacht.capacity} guests</span>
                  </div>
                  <div className="flex items-center">
                    <AnchorIcon className="w-4 h-4 mr-1" />
                    <span>{yacht.length}</span>
                  </div>
                </div>

                {/* Features */}
                <div className="flex flex-wrap gap-2 mb-6">
                  {yacht.features.slice(0, 3).map((feature, index) => (
                    <span
                      key={index}
                      className="bg-blue-50 text-blue-700 px-2 py-1 rounded-md text-xs font-medium"
                    >
                      {feature}
                    </span>
                  ))}
                  {yacht.features.length > 3 && (
                    <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-md text-xs font-medium">
                      +{yacht.features.length - 3} more
                    </span>
                  )}
                </div>

                {/* Action Button */}
                <Button
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 rounded-xl transition-all duration-200 group"
                >
                  View Details
                  <ArrowRightIcon className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <Button
            size="lg"
            variant="outline"
            className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300"
          >
            View Complete Fleet
            <ArrowRightIcon className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </div>
    </section>
  );
}

export default YachtShowcase;
