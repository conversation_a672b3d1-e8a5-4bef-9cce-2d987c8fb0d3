import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useTranslation } from 'react-i18next';
import { ExperienceCard } from './experience-card';
// Sample data - in a real app, this would come from an API
const EXPERIENCES = [
    {
        id: 1,
        title: 'Sunset Sailing Experience',
        description: 'Enjoy a breathtaking sunset over the Auckland skyline with our premium sailing experience. Includes light refreshments and a complimentary glass of New Zealand wine.',
        duration: '2 hours',
        price: '$149',
        image: '/images/experiences/experience-1.jpg',
        featured: true
    },
    {
        id: 2,
        title: 'Full Day Island Adventure',
        description: 'Spend a full day exploring the beautiful islands of the Hauraki Gulf. Includes lunch, snorkeling equipment, and guided walks.',
        duration: '8 hours',
        price: '$299',
        image: '/images/experiences/experience-2.jpg',
        featured: false
    },
    {
        id: 3,
        title: 'Whale Watching Expedition',
        description: 'Join us for an unforgettable whale watching experience. Our expert guides will take you to the best spots to see these magnificent creatures in their natural habitat.',
        duration: '4 hours',
        price: '$199',
        image: '/images/experiences/experience-3.jpg',
        featured: false
    },
    {
        id: 4,
        title: 'Private Charter',
        description: 'Experience the ultimate in luxury with a private charter. Perfect for special occasions, corporate events, or just a day out with friends and family.',
        duration: 'Custom',
        price: 'From $999',
        image: '/images/experiences/experience-4.jpg',
        featured: true
    },
    {
        id: 5,
        title: 'Learn to Sail',
        description: 'Always wanted to learn how to sail? Join our experienced instructors for a hands-on sailing lesson in the beautiful waters of Auckland.',
        duration: '6 hours',
        price: '$249',
        image: '/images/experiences/experience-5.jpg',
        featured: false
    },
    {
        id: 6,
        title: 'Overnight Sailing',
        description: 'Experience the magic of sleeping under the stars on our overnight sailing adventure. Includes dinner, breakfast, and all necessary equipment.',
        duration: 'Overnight',
        price: '$499',
        image: '/images/experiences/experience-6.jpg',
        featured: false
    }
];
export function Experiences() {
    const { t } = useTranslation();
    return (_jsx("section", { id: "experiences", className: "py-16 md:py-24 bg-gray-50", children: _jsxs("div", { className: "container mx-auto px-4", children: [_jsxs("div", { className: "text-center mb-16", children: [_jsx("h2", { className: "text-3xl md:text-4xl font-bold text-gray-900 mb-4", children: t('experiences.title', 'Sailing Experiences') }), _jsx("p", { className: "text-xl text-gray-600 max-w-3xl mx-auto", children: t('experiences.subtitle', 'Discover our range of unforgettable sailing adventures in the beautiful waters of Auckland.') })] }), _jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8", children: EXPERIENCES.map((experience) => (_jsx(ExperienceCard, { title: t(`experiences.${experience.id}.title`, experience.title), description: t(`experiences.${experience.id}.description`, experience.description), duration: t(`experiences.${experience.id}.duration`, experience.duration), price: experience.price, image: experience.image, featured: experience.featured }, experience.id))) }), _jsxs("div", { className: "text-center mt-16", children: [_jsx("h3", { className: "text-2xl font-bold text-gray-900 mb-6", children: t('experiences.cta.title', 'Ready for an Adventure?') }), _jsx("p", { className: "text-gray-600 mb-8 max-w-2xl mx-auto", children: t('experiences.cta.subtitle', 'Book your sailing experience today and create memories that will last a lifetime.') }), _jsx("button", { className: "bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors", children: t('experiences.cta.button', 'View All Experiences') })] })] }) }));
}
export default Experiences;
