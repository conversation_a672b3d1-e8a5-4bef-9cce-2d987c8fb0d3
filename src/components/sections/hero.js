import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ArrowRightIcon } from '../icons';
import { OptimizedImage } from '../ui/OptimizedImage';
export function Hero({ className }) {
    const { t } = useTranslation();
    return (_jsxs("section", { className: cn("relative h-screen flex items-center justify-center overflow-hidden", className), children: [_jsxs("div", { className: "absolute inset-0 z-0", children: [_jsx("div", { className: "absolute inset-0 bg-gradient-to-b from-transparent to-black/70 z-10" }), _jsxs("video", { autoPlay: true, loop: true, muted: true, playsInline: true, className: "w-full h-full object-cover", poster: "/images/hero-poster.jpg", children: [_jsx("source", { src: "/videos/hero-video.mp4", type: "video/mp4" }), _jsx(OptimizedImage, { src: "/images/hero-fallback.jpg", alt: "Sailing in Auckland waters", width: 1920, height: 1080, className: "w-full h-full object-cover" })] })] }), _jsxs("div", { className: "container mx-auto px-4 relative z-20 text-center text-white", children: [_jsx("h1", { className: "text-4xl md:text-6xl lg:text-7xl font-playfair font-bold mb-6 leading-tight", children: t('hero.title') }), _jsx("p", { className: "text-xl md:text-2xl max-w-3xl mx-auto mb-8 font-light", children: t('hero.subtitle') }), _jsxs("div", { className: "flex flex-col sm:flex-row gap-4 justify-center", children: [_jsxs(Button, { size: "lg", className: "bg-blue-600 hover:bg-blue-700 text-lg px-8 py-6", children: [t('hero.cta.primary'), _jsx(ArrowRightIcon, { className: "ml-2 h-5 w-5" })] }), _jsx(Button, { variant: "outline", size: "lg", className: "border-white text-white hover:bg-white/10 text-lg px-8 py-6", children: t('hero.cta.secondary') })] })] }), _jsx("div", { className: "absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20", children: _jsx("div", { className: "animate-bounce", children: _jsx("svg", { className: "w-10 h-10 text-white", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", xmlns: "http://www.w3.org/2000/svg", children: _jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M19 14l-7 7m0 0l-7-7m7 7V3" }) }) }) })] }));
}
export default Hero;
