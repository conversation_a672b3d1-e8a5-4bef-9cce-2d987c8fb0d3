import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ArrowRightIcon } from '../icons';
import { OptimizedImage } from '../ui/OptimizedImage';

interface HeroProps {
  className?: string;
}

export function Hero({ className }: HeroProps) {
  const { t } = useTranslation();
  
  return (
    <section className={cn("relative h-screen flex items-center justify-center overflow-hidden", className)}>
      {/* Video Background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/70 z-10"></div>
        <video
          autoPlay
          loop
          muted
          playsInline
          className="w-full h-full object-cover"
          poster="/images/hero-poster.jpg"
        >
          <source src="/videos/hero-video.mp4" type="video/mp4" />
          {/* Fallback image if video doesn't load */}
          <OptimizedImage 
            src="/images/hero-fallback.jpg" 
            alt="Sailing in Auckland waters" 
            width={1920} 
            height={1080}
            className="w-full h-full object-cover" 
          />
        </video>
      </div>
      
      {/* Content */}
      <div className="container mx-auto px-4 relative z-20 text-center text-white">
        <h1 className="text-4xl md:text-6xl lg:text-7xl font-playfair font-bold mb-6 leading-tight">
          {t('hero.title')}
        </h1>
        <p className="text-xl md:text-2xl max-w-3xl mx-auto mb-8 font-light">
          {t('hero.subtitle')}
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-lg px-8 py-6">
            {t('hero.cta.primary')}
            <ArrowRightIcon className="ml-2 h-5 w-5" />
          </Button>
          <Button 
            variant="outline" 
            size="lg" 
            className="border-white text-white hover:bg-white/20 text-lg px-8 py-6 font-medium shadow-lg [text-shadow:_0_1px_2px_rgb(0_0_0_/_40%)] hover:[text-shadow:_0_1px_3px_rgb(0_0_0_/_60%)]"
          >
            {t('hero.cta.secondary')}
          </Button>
        </div>
      </div>
      
      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="animate-bounce">
          <svg 
            className="w-10 h-10 text-white" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M19 14l-7 7m0 0l-7-7m7 7V3" 
            />
          </svg>
        </div>
      </div>
    </section>
  );
}

export default Hero;
