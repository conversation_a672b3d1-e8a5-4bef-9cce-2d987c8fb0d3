import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ArrowRightIcon } from '../icons';
import { OptimizedImage } from '../ui/OptimizedImage';

interface HeroProps {
  className?: string;
}

export function Hero({ className }: HeroProps) {
  const { t } = useTranslation();

  return (
    <section className={cn("relative h-screen flex items-center justify-center overflow-hidden", className)}>
      {/* Ocean Background with Gradient */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 via-blue-500/40 to-blue-700/60 z-10"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/50 via-transparent to-transparent z-20"></div>

        {/* Sailing Video Background */}
        <video
          autoPlay
          loop
          muted
          playsInline
          className="w-full h-full object-cover"
          poster="https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
        >
          <source src="https://player.vimeo.com/external/434045526.sd.mp4?s=c27eecc69a27dbc4ff2b87d38afc35f1a9e7c02d&profile_id=139&oauth2_token_id=57447761" type="video/mp4" />
          {/* Fallback image if video doesn't load */}
          <OptimizedImage
            src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
            alt="Luxury sailing yacht on crystal blue waters"
            width={1920}
            height={1080}
            className="w-full h-full object-cover"
          />
        </video>

        {/* Animated waves overlay */}
        <div className="absolute bottom-0 left-0 w-full h-32 z-30">
          <svg className="w-full h-full" viewBox="0 0 1200 120" preserveAspectRatio="none">
            <path
              d="M0,60 C150,100 350,0 600,60 C850,120 1050,20 1200,60 L1200,120 L0,120 Z"
              fill="rgba(255,255,255,0.1)"
              className="animate-wave"
            />
            <path
              d="M0,80 C300,120 600,40 900,80 C1050,100 1150,60 1200,80 L1200,120 L0,120 Z"
              fill="rgba(255,255,255,0.05)"
              className="animate-wave"
              style={{ animationDelay: '2s' }}
            />
          </svg>
        </div>
      </div>
      
      {/* Hero Content */}
      <div className="container mx-auto px-4 relative z-30 text-center text-white">
        {/* Floating badge */}
        <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-md rounded-full border border-white/20 mb-6">
          <span className="text-sm font-medium">🏆 #1 Sailing Experience in Auckland</span>
        </div>

        <h1 className="hero-title font-serif font-bold mb-6 leading-tight text-white drop-shadow-lg">
          {t('hero.title', { defaultValue: 'Discover the Magic of Sailing' })}
        </h1>
        <p className="text-xl md:text-2xl max-w-4xl mx-auto mb-8 font-light text-white/90 drop-shadow-md">
          {t('hero.subtitle', {
            defaultValue: 'Experience luxury sailing adventures across Auckland\'s pristine waters. From intimate sunset cruises to multi-day island expeditions.'
          })}
        </p>

        {/* Statistics */}
        <div className="flex flex-wrap justify-center gap-8 mb-10 text-white/90">
          <div className="text-center">
            <div className="text-2xl font-bold">500+</div>
            <div className="text-sm">Happy Sailors</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">15+</div>
            <div className="text-sm">Luxury Yachts</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">50+</div>
            <div className="text-sm">Destinations</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">5★</div>
            <div className="text-sm">Rating</div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            size="lg"
            className="bg-blue-600 hover:bg-blue-700 text-white text-lg px-8 py-4 rounded-xl shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300 font-semibold"
          >
            {t('hero.cta.primary', { defaultValue: 'Book Your Adventure' })}
            <ArrowRightIcon className="ml-2 h-5 w-5" />
          </Button>
          <Button
            variant="outline"
            size="lg"
            className="border-2 border-white text-white hover:bg-white hover:text-blue-700 text-lg px-8 py-4 rounded-xl font-semibold backdrop-blur-sm bg-white/10 shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {t('hero.cta.secondary', { defaultValue: 'View Our Fleet' })}
          </Button>
        </div>
      </div>
      
      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30">
        <div className="animate-bounce">
          <div className="w-12 h-12 rounded-full bg-white/20 backdrop-blur-md border border-white/30 flex items-center justify-center">
            <svg
              className="w-6 h-6 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 14l-7 7m0 0l-7-7m7 7V3"
              />
            </svg>
          </div>
        </div>
      </div>
    </section>
  );
}

export default Hero;
