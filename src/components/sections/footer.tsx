import React from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { SailingSeraiLogo } from '../SailingSeraiLogo';
import { 
  PhoneIcon, 
  MailIcon, 
  MapPinIcon, 
  FacebookIcon, 
  TwitterIcon, 
  InstagramIcon,
  AnchorIcon,
  YachtIcon
} from '../icons';

interface FooterProps {
  className?: string;
}

const footerLinks = {
  services: [
    { label: 'Luxury Charters', href: '#charters' },
    { label: 'Sunset Cruises', href: '#sunset' },
    { label: 'Corporate Events', href: '#corporate' },
    { label: 'Private Tours', href: '#tours' },
    { label: 'Sailing Lessons', href: '#lessons' }
  ],
  destinations: [
    { label: 'Waiheke Island', href: '#waiheke' },
    { label: 'Rangitoto Island', href: '#rangitoto' },
    { label: 'Hauraki Gulf', href: '#hauraki' },
    { label: 'Great Barrier Island', href: '#barrier' },
    { label: 'Kawau Island', href: '#kawau' }
  ],
  company: [
    { label: 'About Us', href: '#about' },
    { label: 'Our Fleet', href: '#fleet' },
    { label: 'Safety', href: '#safety' },
    { label: 'Careers', href: '#careers' },
    { label: 'Contact', href: '#contact' }
  ]
};

const socialLinks = [
  { icon: <FacebookIcon className="w-5 h-5" />, href: '#facebook', label: 'Facebook' },
  { icon: <TwitterIcon className="w-5 h-5" />, href: '#twitter', label: 'Twitter' },
  { icon: <InstagramIcon className="w-5 h-5" />, href: '#instagram', label: 'Instagram' }
];

export function Footer({ className }: FooterProps) {
  const { t } = useTranslation();

  return (
    <footer className={cn("bg-nautical-900 text-white relative overflow-hidden", className)}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          <defs>
            <pattern id="nautical-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
              <circle cx="10" cy="10" r="1" fill="white" />
              <path d="M5 5 L15 15 M15 5 L5 15" stroke="white" strokeWidth="0.5" />
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#nautical-pattern)" />
        </svg>
      </div>

      {/* Wave decoration */}
      <div className="absolute top-0 left-0 w-full h-16 z-10">
        <svg className="w-full h-full" viewBox="0 0 1200 120" preserveAspectRatio="none">
          <path 
            d="M0,60 C150,100 350,0 600,60 C850,120 1050,20 1200,60 L1200,0 L0,0 Z" 
            fill="white" 
          />
        </svg>
      </div>

      <div className="container mx-auto px-4 pt-20 pb-8 relative z-20">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-12">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <SailingSeraiLogo size="lg" showText={true} variant="white" className="mb-6" />
            <p className="text-slate-300 mb-6 leading-relaxed">
              {t('footer.description', { 
                defaultValue: 'Experience the magic of sailing with Auckland\'s premier luxury charter service. Creating unforgettable memories on pristine waters since 2010.' 
              })}
            </p>
            
            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center text-slate-300">
                <PhoneIcon className="w-5 h-5 mr-3 text-ocean-400" />
                <span>+64 9 123 4567</span>
              </div>
              <div className="flex items-center text-slate-300">
                <MailIcon className="w-5 h-5 mr-3 text-ocean-400" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-start text-slate-300">
                <MapPinIcon className="w-5 h-5 mr-3 text-ocean-400 mt-0.5" />
                <span>Viaduct Harbour, Auckland<br />New Zealand</span>
              </div>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <YachtIcon className="w-5 h-5 mr-2 text-ocean-400" />
              {t('footer.services', { defaultValue: 'Services' })}
            </h3>
            <ul className="space-y-2">
              {footerLinks.services.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href} 
                    className="text-slate-300 hover:text-ocean-400 transition-colors duration-200 text-sm"
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Destinations */}
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <AnchorIcon className="w-5 h-5 mr-2 text-ocean-400" />
              {t('footer.destinations', { defaultValue: 'Destinations' })}
            </h3>
            <ul className="space-y-2">
              {footerLinks.destinations.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href} 
                    className="text-slate-300 hover:text-ocean-400 transition-colors duration-200 text-sm"
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="text-lg font-semibold mb-4">
              {t('footer.company', { defaultValue: 'Company' })}
            </h3>
            <ul className="space-y-2">
              {footerLinks.company.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href} 
                    className="text-slate-300 hover:text-ocean-400 transition-colors duration-200 text-sm"
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="bg-ocean-800/30 rounded-2xl p-8 mb-12 border border-ocean-700/30">
          <div className="text-center mb-6">
            <h3 className="text-2xl font-serif font-bold mb-2">
              {t('footer.newsletter.title', { defaultValue: 'Stay Connected' })}
            </h3>
            <p className="text-slate-300">
              {t('footer.newsletter.subtitle', { 
                defaultValue: 'Get the latest updates on sailing adventures and exclusive offers.' 
              })}
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input 
              type="email" 
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-ocean-400"
            />
            <button className="px-6 py-3 bg-ocean-600 hover:bg-ocean-700 text-white font-medium rounded-lg transition-colors duration-200">
              Subscribe
            </button>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-slate-700 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-slate-400 text-sm mb-4 md:mb-0">
              {t('footer.copyright', { 
                year: new Date().getFullYear(),
                defaultValue: `© ${new Date().getFullYear()} Sailing Serai. All rights reserved.`
              })}
            </div>
            
            {/* Social Links */}
            <div className="flex items-center space-x-4">
              <span className="text-slate-400 text-sm mr-2">Follow us:</span>
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  aria-label={social.label}
                  className="w-10 h-10 bg-slate-800 hover:bg-ocean-600 rounded-full flex items-center justify-center transition-colors duration-200"
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Decorative anchors */}
      <div className="absolute bottom-4 left-4 opacity-10">
        <AnchorIcon className="w-8 h-8 text-white" />
      </div>
      <div className="absolute top-20 right-4 opacity-10">
        <YachtIcon className="w-6 h-6 text-white" />
      </div>
    </footer>
  );
}

export default Footer;
