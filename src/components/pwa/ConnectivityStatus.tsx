import React, { useEffect, useState } from 'react';
import { Wifi, WifiOff, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { isOffline, onConnectionChange } from '@/utils/pwa';

interface ConnectivityStatusProps {
  /**
   * Whether to show a full-screen overlay when offline
   * @default true
   */
  showOverlay?: boolean;
  
  /**
   * Whether to show a banner at the top of the screen when offline
   * @default true
   */
  showBanner?: boolean;
  
  /**
   * Additional class names
   */
  className?: string;
  
  /**
   * Callback when the connection status changes
   */
  onStatusChange?: (isOnline: boolean) => void;
}

/**
 * A component that shows the current connectivity status
 */
export function ConnectivityStatus({
  showOverlay = true,
  showBanner = true,
  className,
  onStatusChange,
}: ConnectivityStatusProps) {
  const [isOnline, setIsOnline] = useState(!isOffline());
  const [isReconnecting, setIsReconnecting] = useState(false);

  // Handle connection status changes
  useEffect(() => {
    const handleConnectionChange = (online: boolean) => {
      if (online) {
        // Show reconnecting state briefly when coming back online
        setIsReconnecting(true);
        setTimeout(() => {
          setIsReconnecting(false);
          setIsOnline(true);
        }, 1500);
      } else {
        setIsOnline(false);
      }
      
      onStatusChange?.(online);
    };

    // Set up event listeners
    const cleanup = onConnectionChange(handleConnectionChange);
    
    // Initial check
    handleConnectionChange(!isOffline());
    
    return cleanup;
  }, [onStatusChange]);

  // Don't render anything if online and not reconnecting
  if (isOnline && !isReconnecting) {
    return null;
  }

  // Full-screen overlay
  if (showOverlay && !isOnline) {
    return (
      <div className={cn(
        'fixed inset-0 bg-white dark:bg-gray-900 z-50',
        'flex flex-col items-center justify-center p-6 text-center',
        'transition-opacity duration-300',
        className
      )}>
        <div className="mb-6 p-4 bg-red-100 dark:bg-red-900/30 rounded-full">
          <WifiOff className="w-12 h-12 text-red-600 dark:text-red-400" />
        </div>
        
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          You're Offline
        </h2>
        
        <p className="text-gray-600 dark:text-gray-300 max-w-md mb-6">
          It seems you've lost your internet connection. Please check your network settings and try again.
        </p>
        
        <button
          type="button"
          onClick={() => window.location.reload()}
          className={cn(
            'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm',
            'text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
            'transition-colors duration-200',
            'dark:bg-blue-700 dark:hover:bg-blue-600 dark:focus:ring-offset-gray-900',
            {
              'opacity-50 cursor-not-allowed': isReconnecting,
            }
          )}
          disabled={isReconnecting}
        >
          {isReconnecting ? (
            <>
              <RefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
              Reconnecting...
            </>
          ) : (
            'Try Again'
          )}
        </button>
      </div>
    );
  }

  // Banner at the top of the screen
  if (showBanner && (!isOnline || isReconnecting)) {
    return (
      <div className={cn(
        'fixed top-0 left-0 right-0 z-50',
        'bg-yellow-50 dark:bg-yellow-900/30 border-b border-yellow-200 dark:border-yellow-800',
        'px-4 py-2',
        'transition-transform duration-300',
        className
      )}>
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            {isReconnecting ? (
              <RefreshCw className="animate-spin h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2" />
            ) : (
              <WifiOff className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2" />
            )}
            <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              {isReconnecting 
                ? 'Reconnecting to the network...' 
                : 'You are currently offline. Some features may not be available.'}
            </p>
          </div>
          
          <button
            type="button"
            onClick={() => window.location.reload()}
            className={cn(
              'ml-4 px-3 py-1 text-sm font-medium rounded-md',
              'text-yellow-700 bg-yellow-100 hover:bg-yellow-200',
              'dark:text-yellow-200 dark:bg-yellow-800/50 dark:hover:bg-yellow-800',
              'transition-colors duration-200',
              'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500',
              'dark:focus:ring-offset-gray-900',
              {
                'opacity-50 cursor-not-allowed': isReconnecting,
              }
            )}
            disabled={isReconnecting}
          >
            {isReconnecting ? 'Reconnecting...' : 'Refresh'}
          </button>
        </div>
      </div>
    );
  }

  // Small indicator in the corner
  return (
    <div className={cn(
      'fixed bottom-4 left-4 z-50',
      'p-2 rounded-full',
      'bg-yellow-100 dark:bg-yellow-900/50 border border-yellow-200 dark:border-yellow-800',
      'shadow-lg',
      'transition-all duration-300',
      'flex items-center justify-center',
      'group',
      className
    )}>
      <div className="relative">
        {isReconnecting ? (
          <RefreshCw className="w-5 h-5 text-yellow-600 dark:text-yellow-300 animate-spin" />
        ) : (
          <WifiOff className="w-5 h-5 text-yellow-600 dark:text-yellow-300" />
        )}
        
        <div className={cn(
          'absolute -top-2 -right-2 w-3 h-3 rounded-full',
          isReconnecting ? 'bg-yellow-500' : 'bg-red-500',
          'animate-pulse',
          'ring-2 ring-white dark:ring-gray-900',
          'transition-colors duration-300'
        )} />
        
        <div className={cn(
          'absolute -bottom-8 left-1/2 -translate-x-1/2',
          'px-2 py-1 text-xs font-medium rounded-md',
          'bg-gray-900 text-white',
          'opacity-0 group-hover:opacity-100',
          'transition-opacity duration-200',
          'whitespace-nowrap',
          'pointer-events-none',
          'shadow-lg',
          'dark:bg-gray-100 dark:text-gray-900',
          'before:absolute before:-top-1 before:left-1/2 before:-translate-x-1/2',
          'before:w-2 before:h-2 before:bg-inherit before:rotate-45',
          'before:rounded-sm'
        )}>
          {isReconnecting ? 'Reconnecting...' : 'Offline'}
        </div>
      </div>
    </div>
  );
}

/**
 * A small indicator that shows the current connection status
 */
export function ConnectionIndicator({
  className,
  showTooltip = true,
}: {
  className?: string;
  showTooltip?: boolean;
}) {
  const [isOnline, setIsOnline] = useState(!isOffline());
  const [isReconnecting, setIsReconnecting] = useState(false);

  useEffect(() => {
    const handleConnectionChange = (online: boolean) => {
      if (online) {
        setIsReconnecting(true);
        const timer = setTimeout(() => {
          setIsReconnecting(false);
          setIsOnline(true);
        }, 1500);
        
        return () => clearTimeout(timer);
      } else {
        setIsOnline(false);
      }
    };

    const cleanup = onConnectionChange(handleConnectionChange);
    return cleanup;
  }, []);

  if (isOnline && !isReconnecting) {
    return (
      <div className={cn(
        'relative group',
        'inline-flex items-center justify-center',
        className
      )}>
        <div className="w-2 h-2 rounded-full bg-green-500" />
        
        {showTooltip && (
          <div className={cn(
            'absolute bottom-full left-1/2 -translate-x-1/2 mb-2',
            'px-2 py-1 text-xs font-medium rounded-md',
            'bg-gray-900 text-white',
            'opacity-0 group-hover:opacity-100',
            'transition-opacity duration-200',
            'whitespace-nowrap',
            'pointer-events-none',
            'shadow-lg',
            'dark:bg-gray-100 dark:text-gray-900',
            'before:absolute before:top-full before:left-1/2 before:-translate-x-1/2',
            'before:w-2 before:h-2 before:bg-inherit before:-rotate-45',
            'before:rounded-sm',
            'z-50'
          )}>
            Online
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn(
      'relative group',
      'inline-flex items-center justify-center',
      className
    )}>
      <div className="relative">
        <div className={cn(
          'w-2 h-2 rounded-full',
          isReconnecting ? 'bg-yellow-500' : 'bg-red-500',
          'animate-pulse'
        )} />
        
        {isReconnecting && (
          <div className="absolute inset-0 flex items-center justify-center">
            <RefreshCw className="w-3 h-3 text-yellow-600 dark:text-yellow-300 animate-spin" />
          </div>
        )}
      </div>
      
      {showTooltip && (
        <div className={cn(
          'absolute bottom-full left-1/2 -translate-x-1/2 mb-2',
          'px-2 py-1 text-xs font-medium rounded-md',
          'bg-gray-900 text-white',
          'opacity-0 group-hover:opacity-100',
          'transition-opacity duration-200',
          'whitespace-nowrap',
          'pointer-events-none',
          'shadow-lg',
          'dark:bg-gray-100 dark:text-gray-900',
          'before:absolute before:top-full before:left-1/2 before:-translate-x-1/2',
          'before:w-2 before:h-2 before:bg-inherit before:-rotate-45',
          'before:rounded-sm',
          'z-50'
        )}>
          {isReconnecting ? 'Reconnecting...' : 'Offline'}
        </div>
      )}
    </div>
  );
}
