import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { CheckCircle2, Download, X } from 'lucide-react';
import { getPWAInstallPrompt, isRunningStandalone } from '@/utils/pwa';
import { cn } from '@/lib/utils';
/**
 * A component that prompts the user to install the PWA
 */
export function PWAInstallPrompt({ autoShow = true, onInstall, onDismiss, children, className, }) {
    const [isVisible, setIsVisible] = useState(false);
    const [isInstalling, setIsInstalling] = useState(false);
    const [isInstalled, setIsInstalled] = useState(false);
    const [installPrompt, setInstallPrompt] = useState(getPWAInstallPrompt());
    // Check if the app is already installed
    useEffect(() => {
        setIsInstalled(isRunningStandalone());
    }, []);
    // Listen for PWA install prompt events
    useEffect(() => {
        const handleCanInstall = () => {
            setInstallPrompt(getPWAInstallPrompt());
            if (autoShow) {
                setIsVisible(true);
            }
        };
        const handleAppInstalled = () => {
            setIsInstalled(true);
            setIsVisible(false);
            onInstall?.();
        };
        window.addEventListener('canInstallPWA', handleCanInstall);
        window.addEventListener('pwaInstalled', handleAppInstalled);
        // Initial check
        const prompt = getPWAInstallPrompt();
        setInstallPrompt(prompt);
        if (autoShow && prompt.canInstall && !isInstalled) {
            setIsVisible(true);
        }
        return () => {
            window.removeEventListener('canInstallPWA', handleCanInstall);
            window.removeEventListener('pwaInstalled', handleAppInstalled);
        };
    }, [autoShow, isInstalled, onInstall]);
    // Handle install button click
    const handleInstall = async () => {
        if (!installPrompt?.canInstall)
            return;
        try {
            setIsInstalling(true);
            const installed = await installPrompt.prompt();
            if (installed) {
                setIsInstalled(true);
                onInstall?.();
            }
            else {
                onDismiss?.();
            }
        }
        catch (error) {
            console.error('Error installing PWA:', error);
        }
        finally {
            setIsInstalling(false);
            setIsVisible(false);
        }
    };
    // Don't render anything if the app is already installed
    if (isInstalled) {
        return null;
    }
    // Don't render anything if the install prompt isn't available
    if (!installPrompt?.canInstall) {
        return null;
    }
    // Custom content
    if (children) {
        return (_jsx("div", { className: className, onClick: handleInstall, children: children }));
    }
    return (_jsx(Dialog, { open: isVisible, onOpenChange: setIsVisible, children: _jsxs(DialogContent, { className: "sm:max-w-md", children: [_jsxs(DialogHeader, { children: [_jsx("div", { className: "flex items-center justify-center mb-4", children: _jsx("div", { className: "p-3 bg-blue-100 rounded-full", children: _jsx(Download, { className: "w-8 h-8 text-blue-600" }) }) }), _jsx(DialogTitle, { className: "text-center", children: "Install Sailing Serai" }), _jsx(DialogDescription, { className: "text-center", children: "Add Sailing Serai to your home screen for easy access and a better experience." })] }), _jsxs("div", { className: "space-y-4 py-4", children: [_jsxs("div", { className: "flex items-center space-x-3", children: [_jsx(CheckCircle2, { className: "w-5 h-5 text-green-500 flex-shrink-0" }), _jsx("span", { children: "Quick access to your sailing adventures" })] }), _jsxs("div", { className: "flex items-center space-x-3", children: [_jsx(CheckCircle2, { className: "w-5 h-5 text-green-500 flex-shrink-0" }), _jsx("span", { children: "Works offline" })] }), _jsxs("div", { className: "flex items-center space-x-3", children: [_jsx(CheckCircle2, { className: "w-5 h-5 text-green-500 flex-shrink-0" }), _jsx("span", { children: "No app store required" })] })] }), _jsxs(DialogFooter, { className: "sm:justify-center gap-2", children: [_jsx(Button, { type: "button", variant: "outline", onClick: () => {
                                setIsVisible(false);
                                onDismiss?.();
                            }, className: "w-full", children: "Not Now" }), _jsx(Button, { type: "button", onClick: handleInstall, disabled: isInstalling, className: "w-full", children: isInstalling ? 'Installing...' : 'Install' })] })] }) }));
}
/**
 * A floating action button that shows the PWA install prompt
 */
export function PWAInstallButton({ className, ...props }) {
    return (_jsx(PWAInstallPrompt, { ...props, autoShow: false, children: _jsx(Button, { variant: "outline", size: "icon", className: cn('fixed bottom-6 right-6 rounded-full w-14 h-14 shadow-lg z-50', 'bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700', 'transition-all duration-300 ease-in-out transform hover:scale-105', className), "aria-label": "Install app", children: _jsx(Download, { className: "w-6 h-6" }) }) }));
}
/**
 * A banner that shows at the bottom of the screen to prompt PWA installation
 */
export function PWAInstallBanner({ className, ...props }) {
    const [isVisible, setIsVisible] = useState(true);
    if (!isVisible)
        return null;
    return (_jsx(PWAInstallPrompt, { ...props, autoShow: false, children: _jsx("div", { className: cn('fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 shadow-lg z-50', 'border-t border-gray-200 dark:border-gray-700', 'p-4 md:px-6', 'animate-in slide-in-from-bottom duration-300', className), children: _jsxs("div", { className: "max-w-7xl mx-auto flex flex-col md:flex-row items-center justify-between gap-4", children: [_jsxs("div", { className: "flex-1", children: [_jsx("h3", { className: "font-medium text-gray-900 dark:text-white", children: "Install Sailing Serai" }), _jsx("p", { className: "text-sm text-gray-500 dark:text-gray-400", children: "Add to your home screen for easy access and a better experience." })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Button, { variant: "outline", size: "sm", onClick: (e) => {
                                    e.stopPropagation();
                                    setIsVisible(false);
                                    props.onDismiss?.();
                                }, children: "Not Now" }), _jsx(Button, { size: "sm", onClick: (e) => {
                                    e.stopPropagation();
                                    // The click will be handled by the parent PWAInstallPrompt
                                }, children: "Install" })] }), _jsx("button", { type: "button", className: "absolute top-4 right-4 text-gray-400 hover:text-gray-500", onClick: (e) => {
                            e.stopPropagation();
                            setIsVisible(false);
                            props.onDismiss?.();
                        }, "aria-label": "Close", children: _jsx(X, { className: "w-5 h-5" }) })] }) }) }));
}
