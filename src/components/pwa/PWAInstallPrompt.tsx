import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { CheckCircle2, Download, X } from 'lucide-react';
import { getPWAInstallPrompt, isRunningStandalone } from '@/utils/pwa';
import { cn } from '@/lib/utils';

interface PWAInstallPromptProps {
  /**
   * Whether to show the install prompt automatically when available
   * @default true
   */
  autoShow?: boolean;
  
  /**
   * Callback when the user accepts the install prompt
   */
  onInstall?: () => void;
  
  /**
   * Callback when the user dismisses the install prompt
   */
  onDismiss?: () => void;
  
  /**
   * Custom content for the install prompt
   */
  children?: React.ReactNode;
  
  /**
   * Additional class names
   */
  className?: string;
}

/**
 * A component that prompts the user to install the PWA
 */
export function PWAInstallPrompt({
  autoShow = true,
  onInstall,
  onDismiss,
  children,
  className,
}: PWAInstallPromptProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [installPrompt, setInstallPrompt] = useState<ReturnType<typeof getPWAInstallPrompt>>(getPWAInstallPrompt());

  // Check if the app is already installed
  useEffect(() => {
    setIsInstalled(isRunningStandalone());
  }, []);

  // Listen for PWA install prompt events
  useEffect(() => {
    const handleCanInstall = () => {
      setInstallPrompt(getPWAInstallPrompt());
      if (autoShow) {
        setIsVisible(true);
      }
    };

    const handleAppInstalled = () => {
      setIsInstalled(true);
      setIsVisible(false);
      onInstall?.();
    };

    window.addEventListener('canInstallPWA', handleCanInstall);
    window.addEventListener('pwaInstalled', handleAppInstalled);

    // Initial check
    const prompt = getPWAInstallPrompt();
    setInstallPrompt(prompt);
    
    if (autoShow && prompt.canInstall && !isInstalled) {
      setIsVisible(true);
    }

    return () => {
      window.removeEventListener('canInstallPWA', handleCanInstall);
      window.removeEventListener('pwaInstalled', handleAppInstalled);
    };
  }, [autoShow, isInstalled, onInstall]);

  // Handle install button click
  const handleInstall = async () => {
    if (!installPrompt?.canInstall) return;
    
    try {
      setIsInstalling(true);
      const installed = await installPrompt.prompt();
      
      if (installed) {
        setIsInstalled(true);
        onInstall?.();
      } else {
        onDismiss?.();
      }
    } catch (error) {
      console.error('Error installing PWA:', error);
    } finally {
      setIsInstalling(false);
      setIsVisible(false);
    }
  };

  // Don't render anything if the app is already installed
  if (isInstalled) {
    return null;
  }

  // Don't render anything if the install prompt isn't available
  if (!installPrompt?.canInstall) {
    return null;
  }

  // Custom content
  if (children) {
    return (
      <div 
        className={className}
        onClick={handleInstall}
      >
        {children}
      </div>
    );
  }

  return (
    <Dialog open={isVisible} onOpenChange={setIsVisible}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-blue-100 rounded-full">
              <Download className="w-8 h-8 text-blue-600" />
            </div>
          </div>
          <DialogTitle className="text-center">Install Sailing Serai</DialogTitle>
          <DialogDescription className="text-center">
            Add Sailing Serai to your home screen for easy access and a better experience.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="flex items-center space-x-3">
            <CheckCircle2 className="w-5 h-5 text-green-500 flex-shrink-0" />
            <span>Quick access to your sailing adventures</span>
          </div>
          <div className="flex items-center space-x-3">
            <CheckCircle2 className="w-5 h-5 text-green-500 flex-shrink-0" />
            <span>Works offline</span>
          </div>
          <div className="flex items-center space-x-3">
            <CheckCircle2 className="w-5 h-5 text-green-500 flex-shrink-0" />
            <span>No app store required</span>
          </div>
        </div>
        
        <DialogFooter className="sm:justify-center gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              setIsVisible(false);
              onDismiss?.();
            }}
            className="w-full"
          >
            Not Now
          </Button>
          <Button
            type="button"
            onClick={handleInstall}
            disabled={isInstalling}
            className="w-full"
          >
            {isInstalling ? 'Installing...' : 'Install'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

/**
 * A floating action button that shows the PWA install prompt
 */
export function PWAInstallButton({
  className,
  ...props
}: Omit<PWAInstallPromptProps, 'autoShow' | 'children'>) {
  return (
    <PWAInstallPrompt {...props} autoShow={false}>
      <Button
        variant="outline"
        size="icon"
        className={cn(
          'fixed bottom-6 right-6 rounded-full w-14 h-14 shadow-lg z-50',
          'bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700',
          'transition-all duration-300 ease-in-out transform hover:scale-105',
          className
        )}
        aria-label="Install app"
      >
        <Download className="w-6 h-6" />
      </Button>
    </PWAInstallPrompt>
  );
}

/**
 * A banner that shows at the bottom of the screen to prompt PWA installation
 */
export function PWAInstallBanner({
  className,
  ...props
}: Omit<PWAInstallPromptProps, 'autoShow' | 'children'>) {
  const [isVisible, setIsVisible] = useState(true);
  
  if (!isVisible) return null;
  
  return (
    <PWAInstallPrompt {...props} autoShow={false}>
      <div className={cn(
        'fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 shadow-lg z-50',
        'border-t border-gray-200 dark:border-gray-700',
        'p-4 md:px-6',
        'animate-in slide-in-from-bottom duration-300',
        className
      )}>
        <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="flex-1">
            <h3 className="font-medium text-gray-900 dark:text-white">Install Sailing Serai</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Add to your home screen for easy access and a better experience.
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setIsVisible(false);
                props.onDismiss?.();
              }}
            >
              Not Now
            </Button>
            <Button
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                // The click will be handled by the parent PWAInstallPrompt
              }}
            >
              Install
            </Button>
          </div>
          
          <button
            type="button"
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-500"
            onClick={(e) => {
              e.stopPropagation();
              setIsVisible(false);
              props.onDismiss?.();
            }}
            aria-label="Close"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>
    </PWAInstallPrompt>
  );
}
