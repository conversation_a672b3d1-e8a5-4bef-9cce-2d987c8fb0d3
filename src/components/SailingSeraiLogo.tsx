import React from 'react';

interface SailingSeraiLogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showText?: boolean;
  variant?: 'default' | 'white' | 'dark';
}

export function SailingSeraiLogo({
  className = '',
  size = 'md',
  showText = true,
  variant = 'default'
}: SailingSeraiLogoProps) {
  const sizeClasses = {
    sm: showText ? 'h-8' : 'w-8 h-8',
    md: showText ? 'h-10' : 'w-10 h-10',
    lg: showText ? 'h-12' : 'w-12 h-12',
    xl: showText ? 'h-16' : 'w-16 h-16'
  };

  const textColorClasses = {
    default: 'text-gray-800',
    white: 'text-white',
    dark: 'text-gray-800'
  };

  if (!showText) {
    return (
      <div className={`${sizeClasses[size]} ${className}`}>
        <svg
          viewBox="0 0 120 120"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="w-full h-full"
        >
          {/* Ocean waves background */}
          <circle cx="60" cy="60" r="58" fill="url(#oceanGradient)" stroke="url(#borderGradient)" strokeWidth="2"/>

          {/* Main sail */}
          <path
            d="M60 20 L35 75 L60 70 Z"
            fill="url(#sailGradient)"
            stroke="#0369a1"
            strokeWidth="1"
          />

          {/* Jib sail */}
          <path
            d="M60 25 L75 65 L60 60 Z"
            fill="url(#jibGradient)"
            stroke="#0369a1"
            strokeWidth="1"
          />

          {/* Mast */}
          <line
            x1="60" y1="20"
            x2="60" y2="85"
            stroke="#475569"
            strokeWidth="2"
          />

          {/* Hull */}
          <path
            d="M35 85 Q60 90 85 85 L80 88 Q60 93 40 88 Z"
            fill="#475569"
          />

          {/* Water line */}
          <path
            d="M20 88 Q40 85 60 88 Q80 91 100 88"
            stroke="url(#waveGradient)"
            strokeWidth="2"
            fill="none"
          />

          <defs>
            <linearGradient id="oceanGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#0ea5e9" />
              <stop offset="50%" stopColor="#0284c7" />
              <stop offset="100%" stopColor="#0369a1" />
            </linearGradient>
            <linearGradient id="sailGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#ffffff" />
              <stop offset="100%" stopColor="#f1f5f9" />
            </linearGradient>
            <linearGradient id="jibGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#f8fafc" />
              <stop offset="100%" stopColor="#e2e8f0" />
            </linearGradient>
            <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#0284c7" />
              <stop offset="100%" stopColor="#0369a1" />
            </linearGradient>
            <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#06b6d4" />
              <stop offset="50%" stopColor="#0891b2" />
              <stop offset="100%" stopColor="#0e7490" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <div className={sizeClasses[size]}>
        <svg
          viewBox="0 0 120 120"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="w-full h-full"
        >
          {/* Ocean waves background */}
          <circle cx="60" cy="60" r="58" fill="url(#oceanGradient2)" stroke="url(#borderGradient2)" strokeWidth="2"/>

          {/* Main sail */}
          <path
            d="M60 20 L35 75 L60 70 Z"
            fill="url(#sailGradient2)"
            stroke="#0369a1"
            strokeWidth="1"
          />

          {/* Jib sail */}
          <path
            d="M60 25 L75 65 L60 60 Z"
            fill="url(#jibGradient2)"
            stroke="#0369a1"
            strokeWidth="1"
          />

          {/* Mast */}
          <line
            x1="60" y1="20"
            x2="60" y2="85"
            stroke="#475569"
            strokeWidth="2"
          />

          {/* Hull */}
          <path
            d="M35 85 Q60 90 85 85 L80 88 Q60 93 40 88 Z"
            fill="#475569"
          />

          {/* Water line */}
          <path
            d="M20 88 Q40 85 60 88 Q80 91 100 88"
            stroke="url(#waveGradient2)"
            strokeWidth="2"
            fill="none"
          />

          <defs>
            <linearGradient id="oceanGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#0ea5e9" />
              <stop offset="50%" stopColor="#0284c7" />
              <stop offset="100%" stopColor="#0369a1" />
            </linearGradient>
            <linearGradient id="sailGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#ffffff" />
              <stop offset="100%" stopColor="#f1f5f9" />
            </linearGradient>
            <linearGradient id="jibGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#f8fafc" />
              <stop offset="100%" stopColor="#e2e8f0" />
            </linearGradient>
            <linearGradient id="borderGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#0284c7" />
              <stop offset="100%" stopColor="#0369a1" />
            </linearGradient>
            <linearGradient id="waveGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#06b6d4" />
              <stop offset="50%" stopColor="#0891b2" />
              <stop offset="100%" stopColor="#0e7490" />
            </linearGradient>
          </defs>
        </svg>
      </div>

      <div className="flex flex-col">
        <span className={`font-sailing font-bold text-xl leading-tight ${textColorClasses[variant]}`}>
          Sailing Serai
        </span>
        <span className={`font-sans text-xs tracking-wider uppercase ${textColorClasses[variant]} opacity-75`}>
          Ocean Adventures
        </span>
      </div>
    </div>
  );
}
