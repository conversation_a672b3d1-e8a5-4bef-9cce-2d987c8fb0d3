import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Testimonial, TestimonialFormData } from '@/types/testimonial';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Star, Loader2, Image as ImageIcon, X, Upload } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';
import { uploadImage } from '@/api/upload';
import { OptimizedImage } from '@/components/ui/OptimizedImage';

// Form validation schema
const testimonialFormSchema = z.object({
  content: z.string().min(10, 'Testimonial must be at least 10 characters'),
  rating: z.number().min(1, 'Rating is required').max(5, 'Maximum rating is 5'),
  authorName: z.string().min(2, 'Name is required'),
  authorRole: z.string().optional(),
  authorLocation: z.string().optional(),
  featured: z.boolean().default(false),
  status: z.enum(['pending', 'approved', 'rejected']).default('pending'),
  source: z.string().optional(),
  testimonialDate: z.string().optional(),
  response: z.object({
    content: z.string(),
    author: z.string(),
  }).optional(),
});

type TestimonialFormValues = z.infer<typeof testimonialFormSchema>;

interface TestimonialFormProps {
  initialData?: Testimonial | null;
  onSubmit: (data: TestimonialFormData) => Promise<void>;
  isSubmitting: boolean;
  isAdmin?: boolean;
  onCancel?: () => void;
}

export const TestimonialForm: React.FC<TestimonialFormProps> = ({
  initialData,
  onSubmit,
  isSubmitting,
  isAdmin = false,
  onCancel,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(
    initialData?.author.avatar || null
  );
  const [showResponse, setShowResponse] = useState(!!initialData?.response);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    setValue,
    reset,
    watch,
  } = useForm<TestimonialFormValues>({
    resolver: zodResolver(testimonialFormSchema),
    defaultValues: {
      content: initialData?.content || '',
      rating: initialData?.rating || 5,
      authorName: initialData?.author.name || '',
      authorRole: initialData?.author.role || '',
      authorLocation: initialData?.author.location || '',
      featured: initialData?.featured || false,
      status: initialData?.status || 'pending',
      source: initialData?.source || '',
      testimonialDate: initialData?.testimonialDate || new Date().toISOString().split('T')[0],
      response: initialData?.response ? {
        content: initialData.response.content,
        author: initialData.response.author,
      } : undefined,
    },
  });

  // Watch the rating value to update the star display
  const rating = watch('rating');

  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload an image file (JPEG, PNG, etc.)',
        variant: 'destructive',
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: 'File too large',
        description: 'Maximum file size is 5MB',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsUploading(true);
      
      // In a real app, you would upload the image to your server
      const imageUrl = await uploadImage(file);
      
      // For now, we'll just create a local URL for preview
      const previewUrl = URL.createObjectURL(file);
      setAvatarPreview(previewUrl);
      
      toast({
        title: 'Image uploaded',
        description: 'Profile picture updated successfully',
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: 'Upload failed',
        description: 'There was an error uploading your image. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Handle form submission
  const onSubmitHandler = async (data: TestimonialFormValues) => {
    try {
      await onSubmit({
        ...data,
        authorAvatar: avatarPreview || undefined,
      });
      
      // Reset form if this is a new testimonial
      if (!initialData) {
        reset();
        setAvatarPreview(null);
      }
      
      toast({
        title: initialData ? 'Testimonial updated' : 'Testimonial submitted',
        description: initialData 
          ? 'Your changes have been saved.' 
          : 'Thank you for your feedback! Your testimonial is pending approval.',
      });
    } catch (error) {
      console.error('Error submitting testimonial:', error);
      toast({
        title: 'Something went wrong',
        description: 'There was an error submitting your testimonial. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Render star rating input
  const renderStarRating = () => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((value) => (
          <button
            key={value}
            type="button"
            onClick={() => setValue('rating', value, { shouldValidate: true })}
            className="focus:outline-none"
            aria-label={`Rate ${value} out of 5`}
          >
            <Star
              className={`h-8 w-8 ${
                value <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
              }`}
            />
          </button>
        ))}
        <span className="ml-2 text-sm text-muted-foreground">
          {rating}.0 out of 5
        </span>
      </div>
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmitHandler)} className="space-y-6">
      <div className="space-y-4">
        {/* Rating */}
        <div>
          <Label htmlFor="rating">Your Rating *</Label>
          <div className="mt-1">
            <Controller
              name="rating"
              control={control}
              render={({ field }) => (
                <div>
                  <input type="hidden" {...field} />
                  {renderStarRating()}
                </div>
              )}
            />
            {errors.rating && (
              <p className="mt-1 text-sm text-red-600">{errors.rating.message}</p>
            )}
          </div>
        </div>

        {/* Testimonial Content */}
        <div>
          <Label htmlFor="content">
            Your Testimonial *
            <span className="ml-1 text-sm text-muted-foreground">
              (Min. 10 characters)
            </span>
          </Label>
          <Textarea
            id="content"
            rows={5}
            className="mt-1"
            placeholder="Share your experience with us..."
            {...register('content')}
          />
          {errors.content && (
            <p className="mt-1 text-sm text-red-600">{errors.content.message}</p>
          )}
        </div>

        {/* Author Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="authorName">Your Name *</Label>
            <Input
              id="authorName"
              className="mt-1"
              placeholder="John Doe"
              {...register('authorName')}
            />
            {errors.authorName && (
              <p className="mt-1 text-sm text-red-600">{errors.authorName.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="authorRole">Your Role (Optional)</Label>
            <Input
              id="authorRole"
              className="mt-1"
              placeholder="e.g., Travel Enthusiast, Photographer"
              {...register('authorRole')}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="authorLocation">Location (Optional)</Label>
            <Input
              id="authorLocation"
              className="mt-1"
              placeholder="City, Country"
              {...register('authorLocation')}
            />
          </div>

          <div>
            <Label htmlFor="source">Where did you hear about us? (Optional)</Label>
            <Input
              id="source"
              className="mt-1"
              placeholder="e.g., Google, TripAdvisor, Friend"
              {...register('source')}
            />
          </div>
        </div>

        {/* Profile Picture Upload */}
        <div>
          <Label>Profile Picture (Optional)</Label>
          <div className="mt-1 flex items-center">
            <div className="relative h-16 w-16 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-800">
              {avatarPreview ? (
                <>
                  <OptimizedImage
                    src={avatarPreview}
                    alt="Profile preview"
                    width={64}
                    height={64}
                    sizes="64px"
                    className="h-full w-full object-cover"
                    loading="eager"
                  />
                  <button
                    type="button"
                    onClick={() => setAvatarPreview(null)}
                    className="absolute top-0 right-0 p-1 bg-red-500 text-white rounded-full -mt-1 -mr-1 hover:bg-red-600"
                    aria-label="Remove profile picture"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </>
              ) : (
                <div className="h-full w-full flex items-center justify-center text-gray-400">
                  <User className="h-8 w-8" />
                </div>
              )}
            </div>
            <div className="ml-4">
              <input
                type="file"
                id="avatar"
                accept="image/*"
                className="hidden"
                onChange={handleImageUpload}
                disabled={isUploading}
              />
              <label
                htmlFor="avatar"
                className={cn(
                  'inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
                  isUploading && 'opacity-50 cursor-not-allowed'
                )}
              >
                {isUploading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Upload className="h-4 w-4 mr-2" />
                )}
                {avatarPreview ? 'Change' : 'Upload'} Photo
              </label>
            </div>
          </div>
        </div>

        {/* Admin Only Fields */}
        {isAdmin && (
          <>
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
                Admin Controls
              </h3>
              
              <div className="space-y-4">
                {/* Status */}
                <div>
                  <Label>Status</Label>
                  <div className="mt-1 space-y-2">
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="status-pending"
                        value="pending"
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        {...register('status')}
                      />
                      <Label htmlFor="status-pending" className="font-normal">
                        Pending Review
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="status-approved"
                        value="approved"
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        {...register('status')}
                      />
                      <Label htmlFor="status-approved" className="font-normal">
                        Approved
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="status-rejected"
                        value="rejected"
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        {...register('status')}
                      />
                      <Label htmlFor="status-rejected" className="font-normal">
                        Rejected
                      </Label>
                    </div>
                  </div>
                </div>

                {/* Featured */}
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="featured" className="font-normal">
                      Featured Testimonial
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Show this testimonial in featured sections
                    </p>
                  </div>
                  <Controller
                    name="featured"
                    control={control}
                    render={({ field }) => (
                      <Switch
                        id="featured"
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    )}
                  />
                </div>

                {/* Testimonial Date */}
                <div>
                  <Label htmlFor="testimonialDate">Testimonial Date</Label>
                  <Input
                    id="testimonialDate"
                    type="date"
                    className="mt-1 max-w-xs"
                    {...register('testimonialDate')}
                  />
                </div>

                {/* Response */}
                <div>
                  <div className="flex items-center justify-between">
                    <Label>Add Response</Label>
                    <Switch
                      checked={showResponse}
                      onCheckedChange={setShowResponse}
                    />
                  </div>
                  
                  {showResponse && (
                    <div className="mt-2 space-y-2 pl-4 border-l-2 border-blue-200 dark:border-blue-800">
                      <div>
                            <Label htmlFor="response.content">Response Text</Label>
                            <Textarea
                              id="response.content"
                              rows={3}
                              className="mt-1"
                              placeholder="Your response to this testimonial..."
                              {...register('response.content')}
                            />
                          </div>
                          <div>
                            <Label htmlFor="response.author">Response Author</Label>
                            <Input
                              id="response.author"
                              className="mt-1"
                              placeholder="Your name"
                              {...register('response.author')}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {initialData ? 'Saving...' : 'Submitting...'}
              </>
            ) : initialData ? (
              'Save Changes'
            ) : (
              'Submit Testimonial'
            )}
          </Button>
        </div>
      </form>
    );
  };

export default TestimonialForm;
